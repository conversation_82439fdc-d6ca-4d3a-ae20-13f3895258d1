<template>
  <el-container>
    <el-main>
      <div class="fullscreen">
        <van-swipe
          ref="swipeRef"
          :lazy-render="true"
          style="height: 100%"
          @change="onCarouselChange"
        >
          <van-swipe-item
            v-for="image in imgFiles"
            :key="image.name + carouselRefreshKey"
          >
            <ImagePreviewItem
              :image="image"
              :component-loading="componentLoading"
              :animation-loading="animationLoading"
              @remove="removeImage"
            />
          </van-swipe-item>
          <template v-if="imgFiles.length > 5" #indicator="{ active, total }">
            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
          </template>
        </van-swipe>
      </div>
    </el-main>
    <el-aside width="120px">
      <div class="aside-container">
        <div>
          <el-button
            type="primary"
            :loading="componentLoading"
            class="aside-button"
            @click="edit"
          >
            编辑
          </el-button>
        </div>
        <div>
          <el-button
            type="success"
            :loading="componentLoading"
            class="aside-button"
            @click="confirm"
          >
            识别
          </el-button>
          <el-button
            type="danger"
            :loading="componentLoading"
            class="aside-button"
            style="margin-left: 0"
            @click="backToIndex"
          >
            取消
          </el-button>
        </div>
      </div>
      <div class="aside-container-portrait">
        <el-button
          type="primary"
          :loading="componentLoading"
          class="aside-button"
          @click="edit"
        >
          编辑
        </el-button>
        <el-button
          type="success"
          :loading="componentLoading"
          class="aside-button"
          @click="confirm"
        >
          识别
        </el-button>
        <el-button
          type="danger"
          :loading="componentLoading"
          class="aside-button"
          @click="backToIndex"
        >
          取消
        </el-button>
      </div>
    </el-aside>
  </el-container>
  <el-dialog
    v-model="imageEditVisible"
    :show-close="false"
    :destroy-on-close="true"
    :align-center="true"
    :fullscreen="true"
  >
    <template #header> </template>
    <div style="margin: -26px -16px -16px -16px">
      <ImageCropper
        v-if="currentEditFile"
        :key="currentEditFile.name"
        :edit-file="currentEditFile"
        @edit-ok="onEditOK"
        @edit-cancel="onEditCancel"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { after } from "lodash";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { ElLoading, ElNotification } from "element-plus";
import PQueue from "p-queue";
import { useAppStore } from "@/stores/app.js";
import ImageCropper from "@/components/ImagePreview/ImageCropper.vue";
import ImagePreviewItem from "@/components/ImagePreview/ImagePreviewItem.vue";
import { saveOcrData } from "@/lib/RectDatabase.js";
import { getOcrEngineType } from "@/lib/ConfigDatabase.ts";
import { base64ToFile } from "@/lib/FileHelper.js";

const props = defineProps({
  imageFiles: {
    type: Array,
    default: undefined,
  },
});

const emits = defineEmits(["confirm", "cancel"]);

const router = useRouter();
const appStore = useAppStore();

const carouselRefreshKey = ref(0);
const currenCarouselIndex = ref(0);
const imageEditVisible = ref(false);
const currentEditFile = ref(null);
const swipeRef = ref(null);
const componentLoading = ref(false);
const animationLoading = ref(false);
const ocrEngineType = ref("JS");

const newImageFiles = ref([]);
const imgFiles = ref([]);

onMounted(() => {
  if (props.imageFiles && Array.isArray(props.imageFiles)) {
    newImageFiles.value = [...props.imageFiles];
  }

  if (
    props.imageFiles &&
    Array.isArray(props.imageFiles) &&
    props.imageFiles.length > 0
  ) {
    imgFiles.value = [...props.imageFiles];
  } else {
    imgFiles.value = [...appStore.currentFile];
  }
});

onMounted(() => {
  getOcrEngineType().then((value) => (ocrEngineType.value = value));
});

function onCarouselChange(index) {
  currenCarouselIndex.value = index;
}

function edit() {
  imageEditVisible.value = true;
  currentEditFile.value = imgFiles.value[currenCarouselIndex.value];
}

function onEditOK(newFile) {
  imageEditVisible.value = false;
  currentEditFile.value = undefined;
  const loading = ElLoading.service({ lock: true });
  componentLoading.value = true;
  if (newImageFiles.value.length === 0) {
    const newList = appStore.currentFile.map((f, index) =>
      index === currenCarouselIndex.value ? newFile : f
    );
    appStore.currentFile = newList;
    imgFiles.value = newList;
  } else {
    const newList = newImageFiles.value.map((f, index) =>
      index === currenCarouselIndex.value ? newFile : f
    );
    newImageFiles.value = newList;
    imgFiles.value = newList;
  }

  swipeRef.value?.swipeTo(currenCarouselIndex.value);
  loading.close();
  carouselRefreshKey.value++;
  componentLoading.value = false;
}

function onEditCancel() {
  imageEditVisible.value = false;
  currentEditFile.value = undefined;
}

function removeImage() {
  if (animationLoading.value || componentLoading.value) {
    return;
  }

  let type = "create";
  animationLoading.value = true;
  if (newImageFiles.value.length === 0) {
    type = "create";
    const newList = appStore.currentFile.filter(
      (f, index) => index !== currenCarouselIndex.value
    );
    appStore.currentFile = newList;
    imgFiles.value = newList;
  } else {
    type = "add";
    const newList = newImageFiles.value.filter(
      (f, index) => index !== currenCarouselIndex.value
    );
    newImageFiles.value = newList;
    imgFiles.value = newList;
  }

  if (imgFiles.value.length === 0) {
    if (type === "create") {
      router.back();
    } else {
      emits("cancel");
    }
    return;
  }

  const oldIndex = currenCarouselIndex.value;
  if (oldIndex === 0) {
    currenCarouselIndex.value = 0;
    swipeRef.value?.swipeTo(0);
  } else {
    currenCarouselIndex.value = oldIndex - 1;
    swipeRef.value?.swipeTo(oldIndex - 1);
  }

  setTimeout(() => {
    carouselRefreshKey.value++;
    animationLoading.value = false;
  }, 300);
}

async function confirm() {
  componentLoading.value = true;

  let processedList = [];
  if (newImageFiles.value.length === 0) {
    processedList = await processImageFiles([...appStore.currentFile]);
    appStore.currentFile = processedList;
  } else {
    processedList = await processImageFiles([...newImageFiles.value]);
    newImageFiles.value = processedList;
  }

  componentLoading.value = false;
  if (
    !processedList ||
    !Array.isArray(processedList) ||
    processedList.length === 0
  ) {
    return;
  }

  if (newImageFiles.value.length === 0) {
    appStore.currentFileMeta = null;
    await router.push("/app");
  } else {
    emits("confirm", newImageFiles.value);
  }
}

function backToIndex() {
  if (newImageFiles.value.length === 0) {
    router.back();
  } else {
    emits("cancel");
  }
}

async function processImageFiles(imageFiles) {
  const queue = new PQueue({ concurrency: 4 });

  const uid = dayjs().unix();
  imageFiles.forEach((f, index) => {
    f.uid = uid + index;
  });

  // todo: 使用React Native来压缩
  const compressedFiles = [...imageFiles];

  // 使用multipart上传文件
  const done = after(compressedFiles.length, () => {
    componentLoading.value = false;
  });
  const uploadTasks = compressedFiles.map((f) => {
    const formData = new FormData();
    const file = new File([f], f.name, { type: f.type });
    formData.append("file", file);
    const startTime = performance.now();
    const upstream =
      ocrEngineType.value === "WASM"
        ? "/api/ocr/wasm/process"
        : "/api/ocr/js/process";
    return () =>
      fetch(upstream, {
        method: "POST",
        body: formData,
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.resCode && data.resCode === "SUCCESS") {
            const ms = performance.now() - startTime;
            const totalSeconds = Math.floor(ms / 1000);
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            let timeStr = "";
            if (hours > 0) timeStr += `${hours}小时`;
            if (minutes > 0) timeStr += `${minutes}分`;
            if (seconds > 0 || timeStr === "") timeStr += `${seconds}秒`;

            ElNotification({
              title: "识别提示",
              message: `${f.name} 识别时间: ${timeStr}`,
            });
            return data.data;
          } else {
            ElNotification({
              title: "上传提示",
              message: `${f.name} 上传失败，原因：${data.message}`,
            });
            return f;
          }
        })
        .catch(() => undefined)
        .finally(done);
  });

  const processedData = await queue.addAll(uploadTasks);
  await queue.onIdle();

  const uploadedViewFiles = [];
  for (let i = 0; i < processedData.length; i++) {
    const data = processedData[i];
    const f = imageFiles[i];
    if (!data || !f || !data.fileBase64 || !data.ocrData) {
      continue;
    }

    const viewFile = base64ToFile(data.fileBase64, f.name);
    viewFile.uid = f.uid;
    uploadedViewFiles.push(viewFile);

    const image = await createImageBitmap(viewFile);
    const boxes = data.ocrData;
    const uid = dayjs().unix();
    await saveOcrData(
      f.uid,
      boxes
        .filter((box) => box.text.trim() !== "" && box.confidence > 50)
        .map((box, index) => ({
          uid: uid + index,
          text: box.text,
          bbox: box.bbox,
          confidence: box.confidence,
          baseline: box.baseline,
          line: box.line,
        })),
      image.width,
      image.height
    );
  }

  return uploadedViewFiles;
}
</script>

<style scoped>
.fullscreen {
  position: relative;
  width: 100%;
  height: calc(100vh - var(--el-main-padding) * 2);

  --van-swipe-indicator-size: 12px;
  --van-swipe-indicator-inactive-background: grey;
}

.custom-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 100;
}

@media screen and (orientation: landscape) {
  .aside-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 20px 8px;
  }

  .aside-container .aside-button {
    width: 100%;
  }

  .aside-container .aside-button:not(:last-child) {
    margin-bottom: 15px;
  }

  .aside-container-portrait {
    display: none;
  }
}

@media screen and (orientation: portrait) {
  .aside-container {
    display: none;
  }

  .aside-container-portrait {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    padding: 20px 8px;
  }

  .aside-container-portrait .aside-button {
    max-width: 128px;
    width: 20%;
    min-width: 32px;
  }
}

@media screen and (orientation: portrait) {
  .fullscreen {
    height: 100%;
  }

  .el-aside {
    width: 100%;
  }

  .el-container {
    flex-direction: column;
    height: 100vh;
  }
}
</style>
