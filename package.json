{"name": "hangwrite_view", "version": "0.0.0", "type": "module", "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "pack:all": "npm run build && bash scripts/pack-all.sh", "pack:inc": "npm run build && bash scripts/pack-inc.sh"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/components": "^12.8.2", "@vueuse/core": "^12.8.2", "@vueuse/integrations": "^12.8.2", "@vueuse/rxjs": "^12.8.2", "bootstrap-icons": "^1.13.1", "browser-image-compression": "^2.0.2", "dayjs": "^1.11.13", "dexie": "^4.0.11", "element-plus": "^2.10.4", "konva": "^9.3.22", "lodash": "^4.17.21", "mime-types": "^2.1.35", "p-queue": "^8.1.0", "pdfjs-dist": "^5.4.54", "pinia": "^2.3.1", "pinyin-pro": "^3.26.0", "qrcode": "^1.5.4", "sass": "^1.89.2", "splitpanes": "^3.1.8", "tesseract-wasm": "^0.10.0", "ua-parser-js": "^2.0.4", "vant": "^4.9.21", "vue": "^3.5.18", "vue-advanced-cropper": "^2.8.9", "vue-hooks-plus": "^2.4.0", "vue-konva": "^3.2.2", "vue-router": "^4.5.1"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/mime-types": "^2.1.4", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.33.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.2.10"}}