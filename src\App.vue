<template>
  <div
    v-if="appInitialized"
    class="full-screen-div affix-container"
    :class="{ 'fixed-header-menu': fixedHeaderMenu }"
  >
    <splitpanes @resize="sendResizingMessage" @resized="sendResizedMessage">
      <pane :size="100 - rightPaneSize" style="position: relative">
        <div
          v-show="appStore.showIframeCover"
          style="
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: transparent;
          "
        ></div>
        <iframe
          id="mobsf4"
          src="/editor"
          style="border: none"
          :style="iframeStyle"
        ></iframe>
      </pane>
      <pane
        v-if="rightPaneSize > 0"
        :size="rightPaneSize"
        :style="rightPanelStyle"
      >
        <div style="padding: 10px; height: 100%; background-color: white">
          <el-button
            text
            size="large"
            @click="closeRightPanel"
            style="
              margin-top: 10px;
              padding-right: 80px;
              padding-bottom: 24px;
              padding-top: 24px;
            "
          >
            返回
          </el-button>
          <NotebookPanel v-if="appStore.currentFileId" />
        </div>
      </pane>
    </splitpanes>
    <!-- 复制后提示 -->
    <PictureFloatToolbox
      :alive-tool="aliveTool"
      @add-image-file="addImageFile"
      @change-edit-state="changePictureEditMode"
    />
    <ReadingNavBar @fixed-header-menu="handleFixedHeaderMenu" />
    <FloatCircle
      v-show="!notePenStore.isNotePenToolShow"
      :show-file-note-btn="true"
      @change-tool-current="changeToolCurrent"
      @show-file-notes="showFileNotes"
    />
    <CenterDialog
      @change-right-panel="changeRightPanelStatus"
      :dialog-location="dialogLocation"
    />
    <el-dialog
      v-model="showFileNotesDialog"
      :show-close="false"
      :append-to-body="true"
      :destroy-on-close="true"
      :align-center="true"
      :fullscreen="true"
      class="file-notes-dialog"
    >
      <FileNotebookPanel>
        <template v-slot:back>
          <el-button
            text
            type="primary"
            style="padding-left: 0"
            @click="closeFileNotesDialog"
          >
            {{ "< 返回" }}
          </el-button>
        </template>
      </FileNotebookPanel>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, nextTick, onMounted, onUnmounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useEventListener, useWinResize } from "vue-hooks-plus";
import { ElLoading, ElMessage } from "element-plus";
import dayjs from "dayjs";
// import {Pane, Splitpanes} from "splitpanes";
import pane from "@/components/pane.vue";
import splitpanes from "@/components/splitpanes.vue";
import { APP_ACTION_TYPE, LayerType } from "@/constant";
import { useAppStore } from "@/stores/app";
import { useProfileStore } from "@/stores/profile";
import { useNotePenStore } from "@/stores/note-pen";
import { asyncReadAsDataURL, updateFileCoverData } from "@/lib/FileCover.js";
import {
  putFileMeta,
  createNewFileMeta,
  getFileMeta,
  updateParentFolderFileCount,
} from "@/lib/FileList.js";
import { base64ToFile } from "@/lib/FileHelper.js";
import PictureFloatToolbox from "@/components/PictureFloatToolbox.vue";
import NotebookPanel from "@/components/NotebookPanel/NotebookPanel.vue";
import FileNotebookPanel from "@/components/NotebookPanel/FileNotebookPanel.vue";
import ReadingNavBar from "@/components/NavBar/ReadingNavBar.vue";
import FloatCircle from "@/components/ToolSelection/FloatCircle.vue";
import CenterDialog from "@/components/CenterDialogNote/CenterDialog.vue";

const router = useRouter();
const appStore = useAppStore();
const profileStore = useProfileStore();
const notePenStore = useNotePenStore();

const appInitialized = ref(false);
const loadingInstance = ref();
const aliveTool = ref("EMPTY"); //EMPTY:0无 HAND:1手型 PEN:2笔型 BOX_SELECT:3框选 ADD_TEXT:4添加文字 ADD_IMAGE:5图片 TEXT_POP_UP:6文字上方弹窗 HIGHLIGHT_PEN：7荧光笔 LINE：8直线 DOTTED_LINE：9虚线 ERASER：10橡皮擦
const aliveColor = ref("rgb(0, 0, 0)"); //当前工具 使用中颜色
const aliveLineWidth = ref(10); //当前工具 使用中线宽高
const pictureEditMode = ref(false);
const isPanelResizing = ref(false);
const iframeWidth = ref(0);
const rightPaneSize = ref(0);
const rightPanelStyle = ref({});
const showFileNotesDialog = ref(false);
const fixedHeaderMenu = ref(false);

const iframeStyle = computed(() => {
  return {
    width: isPanelResizing.value ? `${iframeWidth.value}px` : "100%",
    height: `${window.innerHeight}px`,
  };
});

onMounted(() => {
  loadingInstance.value = ElLoading.service({ lock: true });

  if (!appStore.currentFile && appStore.currentFileId) {
    const fileUidList = appStore.currentFileMeta.fileUidList;
    if (typeof ReactNativeWebView !== "undefined") {
      const data = {
        action: "set-target-file-id",
        timestamp: appStore.currentFileId.toString(),
        fileUidList:
          fileUidList && fileUidList.length > 0
            ? JSON.stringify(fileUidList)
            : "",
      };
      // eslint-disable-next-line no-undef
      ReactNativeWebView.postMessage(JSON.stringify(data));
    }
  } else if (appStore.currentFile) {
    appInitialized.value = true;
    loadingInstance.value?.close();
  } else {
    ElMessage.error("文件读取错误");
    router.replace("/");
    return;
  }
});

onUnmounted(() => {
  loadingInstance.value?.close();
});

function sendMessageToChild(extraData) {
  const iframe = document.getElementById("mobsf4");
  if (!iframe) {
    return;
  }

  let layerType;
  switch (aliveTool.value) {
    // case "HAND":
    case "BOX_SELECT":
      layerType = LayerType.RECT;
      break;
    case "ADD_IMAGE":
      layerType = LayerType.PICTURE;
      break;
    case "PEN":
    case "HIGHLIGHT_PEN":
    case "HIGHLIGHT_PEN_STRAIGHT":
    case "LINE":
    case "DOTTED_LINE":
    case "ERASER":
      layerType = LayerType.PEN;
      break;
    default:
      layerType = "";
      break;
  }
  const data = {
    layerType,
    layerData: {
      type: aliveTool.value,
      color: aliveColor.value,
      width: aliveLineWidth.value,
      inEditMode: pictureEditMode.value && layerType === LayerType.PICTURE,
      ...extraData,
    },
  };
  iframe.contentWindow.postMessage(data, "*");
}

function sendResizingMessage() {
  if (isPanelResizing.value) {
    return;
  }
  const iframe = document.getElementById("mobsf4");
  if (!iframe) {
    return;
  }
  iframeWidth.value = iframe.getBoundingClientRect().width;
  isPanelResizing.value = true;
}

function sendResizedMessage(e) {
  const iframe = document.getElementById("mobsf4");
  if (!iframe) {
    return;
  }
  const rightPane = e[1];
  if (rightPane?.size < 15) {
    rightPaneSize.value = 0;
  } else {
    rightPaneSize.value = rightPane?.size;
  }
  isPanelResizing.value = false;
  iframeWidth.value = iframe.getBoundingClientRect().width;
}

function changeToolCurrent(tool) {
  aliveTool.value = tool.type;
  aliveColor.value = tool.aliveColor;
  aliveLineWidth.value = tool.lineWidth;
  sendMessageToChild();
}

function addImageFile(file) {
  sendMessageToChild({ imageFile: file });
}

function changePictureEditMode(value) {
  pictureEditMode.value = value;
  sendMessageToChild();
}

const dialogLocation = ref("center");
// 右侧笔记面板
function changeRightPanelStatus(type) {
  showRightPanel(type);
}

const showRightNote = ref(false);
function showRightPanel(type) {
  showRightNote.value = true;
  if (type === "fullscreen") {
    rightPaneSize.value = 100;
    rightPanelStyle.value = { zIndex: 100 };
    dialogLocation.value = "bottom";
  } else if (window.innerWidth < 576) {
    rightPaneSize.value = 100;
    rightPanelStyle.value = { zIndex: 100 };
    dialogLocation.value = "bottom";
  } else if (window.innerWidth >= 576 && window.innerWidth <= 768) {
    rightPaneSize.value = 40;
    rightPanelStyle.value = {};
    dialogLocation.value = "center";
  } else if (window.innerWidth > 768 && window.innerWidth <= 992) {
    rightPaneSize.value = 40;
    rightPanelStyle.value = {};
    dialogLocation.value = "left";
  } else {
    rightPaneSize.value = 30;
    rightPanelStyle.value = {};
    dialogLocation.value = "left";
  }
}

function closeRightPanel() {
  showRightNote.value = false;
  dialogLocation.value = "center";
  rightPaneSize.value = 0;
  rightPanelStyle.value = {};
}

function showFileNotes() {
  showFileNotesDialog.value = true;
}

function closeFileNotesDialog() {
  showFileNotesDialog.value = false;
}

function checkScreenWidth() {
  if (showRightNote.value) {
    showRightPanel();
  }
}

function handleFixedHeaderMenu(value) {
  fixedHeaderMenu.value = value;
}

useWinResize(checkScreenWidth);

useEventListener("message", (event) => {
  const { layerType, type } = event.data;
  if (layerType === LayerType.APP && type === APP_ACTION_TYPE.INIT) {
    if (!appStore.currentFile) {
      return;
    }

    saveNoteFile().then(() => {
      // todo: post message by store action
      const iframe = document.getElementById("mobsf4");
      if (iframe) {
        const data = {
          layerType: LayerType.APP,
          type: APP_ACTION_TYPE.UPLOAD,
          file: Array.isArray(appStore.currentFile)
            ? [...appStore.currentFile]
            : appStore.currentFile,
          fileUid: Array.isArray(appStore.currentFile)
            ? appStore.currentFile.map((file) => file.uid)
            : [],
          fileId: appStore.currentFileId ? appStore.currentFileId : Date.now(),
        };
        iframe.contentWindow.postMessage(data, "*");
      }
    });
  } else if (
    layerType === LayerType.APP &&
    type === APP_ACTION_TYPE.ADD_IMG_LOADED
  ) {
    // todo: 临时处理添加图片后pinchZoom不立即生效的问题，需要一个更好的方案
    rightPaneSize.value =
      Math.random() > 0.5
        ? rightPaneSize.value + 0.1
        : rightPaneSize.value - 0.1;
    const loading = ElLoading.service({ lock: true });
    nextTick(() => loading.close());
  } else if (
    layerType === LayerType.APP &&
    type === APP_ACTION_TYPE.PDF_COVER_DATA
  ) {
    const { data } = event.data;
    if (data) {
      updateFileCoverData(appStore.currentFileId, data);
    }
  } else if (event?.data?.action === "expo-open-file") {
    const { fileId, fileData, filenames } = event.data;
    initFiles(fileId, fileData, filenames);
  }
});

const initFiles = async (fileId, fileData, filenames) => {
  const fileMeta = await getFileMeta(fileId);
  if (!fileMeta) {
    ElMessage.error("文件不存在");
    router.replace("/");
    return;
  }
  appStore.currentFileMeta = fileMeta;

  const files = [];
  for (let i = 0; i < fileData.length; i++) {
    const file64Data = fileData[i];
    const filename = filenames[i];
    const file = base64ToFile(file64Data, filename);
    files.push(file);
  }

  if (files.length === 1 && fileMeta.extension === "pdf") {
    appStore.currentFile = files[0];
  } else {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      // 文件名格式file_timestamp_uid.extension
      const uid = file.name.match(/_([^_.]+)\./)[1];
      if (uid) {
        file.uid = uid;
      }
    }
    appStore.currentFile = files;
  }

  appInitialized.value = true;
  loadingInstance.value?.close();
};

async function saveNoteFile() {
  const timestamp = Date.now();
  let fileMeta, isNew;
  const filename = Array.isArray(appStore.currentFile)
    ? appStore.currentFile[0].name
    : appStore.currentFile.name;
  if (!appStore.currentFileId) {
    fileMeta = createNewFileMeta(
      timestamp,
      appStore.currentFolderId,
      "file",
      filename,
      0
    );
    isNew = true;
  } else {
    fileMeta = await getFileMeta(appStore.currentFileId);
    isNew = false;
    if (!fileMeta || !fileMeta.id) {
      fileMeta = createNewFileMeta(
        timestamp,
        appStore.currentFolderId,
        "file",
        filename,
        0
      );
      isNew = true;
    }
  }

  appStore.currentFileMeta = fileMeta;
  if (isNew) {
    await addNewFile(timestamp.toString(), fileMeta);
  } else {
    await updateNoteFile(timestamp.toString(), fileMeta);
  }
}

async function addNewFile(timestamp, fileMeta) {
  await putFileMeta(fileMeta);
  await updateParentFolderFileCount(fileMeta, 1);

  if (Array.isArray(appStore.currentFile)) {
    const uid = dayjs().unix();
    for (let i = 0; i < appStore.currentFile.length; i++) {
      const file = appStore.currentFile[i];
      const fileData = await asyncReadAsDataURL(file);
      if (fileData?.data) {
        if (i === 0) {
          await updateFileCoverData(fileMeta.id, file);
        }
        if (typeof ReactNativeWebView !== "undefined") {
          const data = {
            action: "save-file-data-to-storage",
            timestamp: timestamp.toString(),
            data: fileData.data,
            index: file.uid ?? uid + i,
          };
          // eslint-disable-next-line no-undef
          ReactNativeWebView.postMessage(JSON.stringify(data));
        }
      }
    }
  } else {
    const fileData = await asyncReadAsDataURL(appStore.currentFile);
    if (fileData?.data) {
      if (typeof ReactNativeWebView !== "undefined") {
        const data = {
          action: "save-file-data-to-storage",
          timestamp: timestamp.toString(),
          data: fileData.data,
          index: dayjs().unix(),
        };
        // eslint-disable-next-line no-undef
        ReactNativeWebView.postMessage(JSON.stringify(data));
      }
    }
  }

  profileStore.reduceFreeTrialCount();
}

async function updateNoteFile(timestamp, fileMeta) {
  // todo: update notes count
  fileMeta.lastModified = timestamp;
  await putFileMeta(fileMeta);
}
</script>

<style>
/* @import "splitpanes/dist/splitpanes.css"; */

/* force enable hardware acceleration */
canvas {
  transform: translate3d(0, 0, 0);
}

.file-notes-dialog {
  padding: 0;
}

.file-notes-dialog .el-dialog__header {
  display: none;
}
</style>

<style scoped>
.full-screen-div {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.fixed-header-menu {
  height: calc(100vh - 70px);
  top: 70px;
}

.parentHeader {
  display: flex;
  justify-content: space-between;
  background-color: rgb(249, 249, 249);
}

.headerLeft {
  display: flex;
  height: 50px;
}

.headerRight {
  display: flex;
  height: 50px;
}

.headerButton {
  height: 100%;
  align-items: center;
  justify-content: center;
  display: flex;
  padding-left: 8px;
  padding-right: 8px;
}

.headerButtonStart {
  height: 100%;
  align-items: center;
  justify-content: center;
  display: flex;
  padding-left: 25px;
  padding-right: 5px;
}

.btn-square-borderNone {
  width: 25px;
  height: 25px;
}

/* override splitpanes height */
.splitpanes {
  /* height: calc(100% - 50px); */
  height: calc(100%);
}
</style>
