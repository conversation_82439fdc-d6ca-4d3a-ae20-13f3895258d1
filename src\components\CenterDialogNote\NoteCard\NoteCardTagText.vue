<template>
  <div class="tag-input-container">
    <div class="el-tags">
      <el-autocomplete
        v-if="!readonly"
        v-model="inputState"
        :fetch-suggestions="querySearch"
        :trigger-on-focus="false"
        :disabled="readonly"
        placement="top-start"
        clearable
        placeholder="输入标签，同类型知识分类"
        @select="handleSelect"
        @clear="onInputCleared"
        @blur="onInputBlurred"
        @change="onInputChanged"
      />
      <el-tag v-else>{{ inputState }}</el-tag>
    </div>
    <div class="el-btn">
      <el-button
        v-if="!readonly"
        type="primary"
        plain
        size="small"
        @click="showDrawer = true"
      >
        选择
      </el-button>
    </div>
    <el-drawer
      v-model="showDrawer"
      title="笔记标签"
      :append-to-body="true"
      :size="isVertical ? '70%' : drawerLandscapeWidth"
      :direction="isVertical ? 'btt' : 'rtl'"
    >
      <NoteCardTagDrawer @select="handleSelect" />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useDebounceFn, useWindowSize } from "@vueuse/core";
// @ts-expect-error import js lib
import { getNoteKeywords } from "@/lib/RectDatabase.js";
import NoteCardTagDrawer from "@/components/CenterDialogNote/NoteCard/NoteCardTagDrawer.vue";

const props = defineProps({
  keyword: {
    type: String,
    default: "",
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["keyword-changed"]);

const inputState = ref(props.keyword);
const showDrawer = ref(false);

const { width, height } = useWindowSize();
const isVertical = computed(() => width.value > height.value);
const drawerLandscapeWidth = computed(() =>
  width.value < 900 ? "300px" : "30%"
);

watch(
  () => props.keyword,
  (newVal) => {
    inputState.value = newVal;
  }
);

function querySearch(queryString: string, cb: (arg: unknown[]) => void) {
  getNoteKeywords(queryString).then((keywords: unknown[]) => {
    cb(keywords);
  });
}

function handleSelect(text: string) {
  inputState.value = text;
  emitKeywordChanged(text);
  showDrawer.value = false;
}

function onInputCleared() {
  emitKeywordChanged("");
}

function onInputBlurred() {
  emitKeywordChanged(inputState.value);
}

function onInputChanged(text: string) {
  emitKeywordChanged(text);
}

const emitKeywordChanged = useDebounceFn((text: string) => {
  emits("keyword-changed", text);
}, 300);
</script>

<style lang="scss" scoped>
.tag-input-container {
  display: flex;
  align-items: center;

  &.warp {
    flex-wrap: wrap;
  }

  .el-tags {
    flex: 1;
  }

  .el-btn {
    margin-left: 5px;

    &.break {
      flex-basis: 100%;
      margin-left: 0;
      margin-top: 5px;
    }
  }
}
</style>
