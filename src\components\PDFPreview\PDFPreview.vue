<template>
  <el-container>
    <el-main>
      <div class="fullscreen">
        <van-swipe ref="swipeRef" :lazy-render="true" style="height: 100%">
          <van-swipe-item v-for="image in imgFiles" :key="image.uid">
            <ImagePreviewItem
              :image="image"
              :component-loading="componentLoading"
              :animation-loading="animationLoading"
              :no-edit="true"
            />
          </van-swipe-item>
          <template v-if="imgFiles.length > 5" #indicator="{ active, total }">
            <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
          </template>
        </van-swipe>
      </div>
    </el-main>
    <el-aside width="120px">
      <div class="aside-container">
        <div>
          <el-button
            type="success"
            :loading="componentLoading"
            class="aside-button"
            @click="confirm"
          >
            确定
          </el-button>
          <el-button
            type="danger"
            :loading="componentLoading"
            class="aside-button"
            style="margin-left: 0"
            @click="backToIndex"
          >
            取消
          </el-button>
        </div>
      </div>
      <div class="aside-container-portrait">
        <el-button
          type="success"
          :loading="componentLoading"
          class="aside-button"
          @click="confirm"
        >
          确定
        </el-button>
        <el-button
          type="danger"
          :loading="componentLoading"
          class="aside-button"
          @click="backToIndex"
        >
          取消
        </el-button>
      </div>
    </el-aside>
  </el-container>
</template>

<script setup>
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import * as pdfjsLib from "pdfjs-dist";
import { useAppStore } from "@/stores/app.js";
import { useProfileStore } from "@/stores/profile";
import ImagePreviewItem from "@/components/ImagePreview/ImagePreviewItem.vue";
import { asyncReadAsDataURL } from "@/lib/FileCover.js";
import { saveOcrData } from "@/lib/RectDatabase.js";

const router = useRouter();
const appStore = useAppStore();
const profileStore = useProfileStore();

const swipeRef = ref(null);
const componentLoading = ref(false);
const animationLoading = ref(false);

const imgFiles = ref([]);
const textData = ref([]);

onMounted(() => {
  loadPDFFile(appStore.currentFile);
});

async function confirm() {
  if (textData.value.length === 0) {
    // 当前文件无文本信息，询问用户是否需要OCR识别
    try {
      await ElMessageBox.confirm(
        "当前文件无文本信息，是否需要OCR识别？",
        "提示",
        {
          confirmButtonText: "OCR识别",
          cancelButtonText: "跳过",
          type: "info",
        }
      );

      // 用户选择OCR识别，转到cropper-preview页面
      appStore.currentFile = [...imgFiles.value];
      appStore.currentFileMeta = null;
      router.push("/cropper-preview");
    } catch {
      // 用户选择跳过，不做任何动作
      return;
    }
  } else if (textData.value.length !== imgFiles.value.length) {
    // 文本信息页数和图片页数不同，询问用户选择处理方式
    const missingPages = getMissingTextPages();
    const missingPagesText =
      missingPages.length > 0
        ? `第${missingPages.join("、")}页缺失文本信息`
        : "部分页面缺失文本信息";

    try {
      await ElMessageBox.confirm(
        `检测到文本信息页数(${textData.value.length}页)与图片页数(${imgFiles.value.length}页)不一致。${missingPagesText}，请选择处理方式：`,
        "处理方式选择",
        {
          confirmButtonText: "使用文本信息",
          cancelButtonText: "使用OCR图片处理",
          type: "warning",
        }
      );

      // 用户选择使用现有文本信息，保存并跳转
      await savePDFFile();
    } catch {
      // 用户选择OCR图片处理，转到cropper-preview页面
      appStore.currentFile = [...imgFiles.value];
      appStore.currentFileMeta = null;
      router.push("/cropper-preview");
    }
  } else {
    // 有文本数据，保存并跳转
    await savePDFFile();
  }
}

function backToIndex() {
  router.back();
}

// 分析哪些页码缺失文本信息
function getMissingTextPages() {
  const totalPages = imgFiles.value.length;
  const pagesWithText = new Set(textData.value.map((item) => item.pageNumber));
  const missingPages = [];

  for (let i = 1; i <= totalPages; i++) {
    if (!pagesWithText.has(i)) {
      missingPages.push(i);
    }
  }

  return missingPages;
}

async function savePDFFile() {
  animationLoading.value 
  // 保存所有页面的OCR数据
  for (const pageData of textData.value) {
    await saveOcrData(
      pageData.uid,
      pageData.data,
      pageData.width,
      pageData.height
    );
  }

  // 将转换后的图片文件设置到store中，然后跳转到图片预览页面
  appStore.currentFile = [...imgFiles.value];
  appStore.currentFileMeta = null;
  router.push("/app");
}

async function loadPDFFile(file) {
  let loading = ElLoading.service({
    lock: true,
    text: "正在处理PDF",
  });

  try {
    const fileData = await asyncReadAsDataURL(file);
    if (!fileData.data) {
      loading.close();
      ElMessage.error("无法读取PDF文件");
      backToIndex();
      return;
    }

    const pdfData = atob(fileData.data.split(",")[1]);
    const loadingTask = pdfjsLib.getDocument({ data: pdfData });
    const pdfDoc = await loadingTask.promise;

    // 检查PDF页数
    const totalPages = pdfDoc.numPages;
    let pagesToProcess = totalPages;

    if (totalPages > 10) {
      loading.close();

      if (profileStore.isVIP) {
        // VIP用户：提醒页数过多可能处理缓慢
        const confirmed = await ElMessageBox.confirm(
          `此PDF共有${totalPages}页，页数较多可能处理缓慢，是否继续？`,
          "提示",
          {
            confirmButtonText: "继续处理",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).catch(() => false);

        if (!confirmed) {
          backToIndex();
          return;
        }
      } else {
        // 非VIP用户：只能处理前10页
        const confirmed = await ElMessageBox.confirm(
          `此PDF共有${totalPages}页，非VIP用户只能处理前10页，是否继续？`,
          "提示",
          {
            confirmButtonText: "处理前10页",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).catch(() => false);

        if (!confirmed) {
          backToIndex();
          return;
        }

        pagesToProcess = 10;
      }

      // 重新显示loading
      loading = ElLoading.service({
        lock: true,
        text: "正在处理PDF",
      });
    }

    // 转换每一页为图片
    for (let i = 1; i <= pagesToProcess; ++i) {
      loading.setText(`正在处理第 ${i}/${pagesToProcess} 页...`);

      const page = await pdfDoc.getPage(i);
      const viewport = page.getViewport({ scale: 1.0 }); // 使用2倍缩放提高清晰度

      // 创建canvas
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // 渲染PDF页面到canvas
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;

      // 将canvas转换为blob
      const blob = await new Promise((resolve) => {
        canvas.toBlob(resolve, "image/jpeg", 0.8);
      });

      // 创建File对象
      const imageFilename =
        i === 1 ? `${file.name}.jpg` : `${file.name}_page_${i}.jpg`;
      const imageFile = new File([blob], imageFilename, {
        type: "image/jpeg",
      });

      // 添加uid用于标识
      imageFile.uid = Date.now() + i;
      imageFile.pageNumber = i; // 添加页码信息
      imgFiles.value = [...imgFiles.value, imageFile];

      const textContent = await page.getTextContent();
      const items = textContent.items.filter(
        (item) => item.width > 0 && item.height > 0
      );
      const uid = dayjs().unix();
      if (items.length === 0) {
        continue;
      }

      // 将行级别的文本数据转换为单词级别的坐标信息
      const pageTextData = convertLineDataToWordData(items, viewport, uid);
      textData.value = [
        ...textData.value,
        {
          uid: imageFile.uid,
          pageNumber: i, // 添加页码信息
          data: pageTextData,
          width: canvas.width,
          height: canvas.height,
        },
      ];
    }

    loading.close();
    swipeRef.value.swipeTo(0);
  } catch (error) {
    loading.close();
    ElMessage.error("PDF转换失败: " + error.message);
    backToIndex();
  }
}

/**
 * 计算字符的相对宽度权重
 * @param {string} char - 字符
 * @returns {number} 字符宽度权重
 */
function getCharacterWidth(char) {
  // 基于字符类型分配不同的宽度权重
  if (/[A-Z]/.test(char)) {
    // 大写字母通常更宽
    return 1.2;
  } else if (/[a-z]/.test(char)) {
    // 小写字母基准宽度
    if (/[ijl]/.test(char)) {
      // 窄字母
      return 0.5;
    } else if (/[mw]/.test(char)) {
      // 宽字母
      return 1.4;
    } else {
      // 普通小写字母
      return 1.0;
    }
  } else if (/[0-9]/.test(char)) {
    // 数字通常等宽
    return 1.0;
  } else if (/[\s]/.test(char)) {
    // 空格
    return 0.3;
  } else if (/[.,;:!?]/.test(char)) {
    // 标点符号通常较窄
    return 0.4;
  } else if (/[()[\]{}]/.test(char)) {
    // 括号类符号
    return 0.6;
  } else {
    // 其他字符默认宽度
    return 1.0;
  }
}

/**
 * 计算从文本开始到指定位置的累积宽度比例
 * @param {string} text - 完整文本
 * @param {number} startIndex - 开始位置
 * @param {number} endIndex - 结束位置
 * @returns {number} 累积宽度比例
 */
function calculateCharacterWidthRatio(text, startIndex, endIndex) {
  let totalWidth = 0;
  let targetWidth = 0;

  for (let i = 0; i < text.length; i++) {
    const charWidth = getCharacterWidth(text[i]);
    totalWidth += charWidth;

    if (i < endIndex) {
      targetWidth += charWidth;
    }
  }

  return totalWidth > 0 ? targetWidth / totalWidth : 0;
}

/**
 * 将PDF.js行级别的文本数据转换为单词级别的坐标信息
 * @param {Array} items - PDF.js获取的文本项数组
 * @param {Object} viewport - PDF页面视口信息
 * @param {number} baseUid - 基础UID用于生成唯一标识
 * @returns {Array} 单词级别的坐标信息数组
 */
function convertLineDataToWordData(items, viewport, baseUid) {
  const wordDataArray = [];
  let wordIndex = 0;

  items.forEach((item) => {
    // 获取行的变换矩阵和坐标
    const matrix = pdfjsLib.Util.transform(viewport.transform, item.transform);
    const lineX0 = matrix[4];
    const lineY0 = matrix[5] - item.height;
    const lineX1 = matrix[4] + item.width;
    const lineY1 = matrix[5];

    // 行信息
    const lineInfo = {
      text: item.str,
      bbox: { x0: lineX0, y0: lineY0, x1: lineX1, y1: lineY1 },
      confidence: 100,
      rowAttributes: { rowHeight: item.height },
    };

    // 将文本按空格分割成单词
    const words = item.str
      .split(/\s+/)
      .filter((word) => word.trim().length > 0);

    if (words.length === 0) {
      // 如果没有单词，创建一个空的文本项
      wordDataArray.push({
        uid: baseUid + wordIndex++,
        text: item.str,
        bbox: { x0: lineX0, y0: lineY0, x1: lineX1, y1: lineY1 },
        confidence: 100,
        line: lineInfo,
      });
      return;
    }

    // 计算每个单词的大致坐标
    const lineWidth = item.width;

    // 使用正确的算法计算每个单词的位置，避免重复单词问题
    let searchStartPosition = 0;

    words.forEach((word) => {
      // 在当前搜索位置之后查找单词的实际位置
      const wordStartInLine = item.str.indexOf(word, searchStartPosition);

      // 如果找不到单词，跳过这个单词（理论上不应该发生）
      if (wordStartInLine === -1) {
        return;
      }

      const wordEndInLine = wordStartInLine + word.length;

      // 更新搜索位置到这个单词的结束位置，确保下次搜索从正确位置开始
      searchStartPosition = wordEndInLine;

      // 根据字符宽度权重估算单词的X坐标
      const wordStartRatio = calculateCharacterWidthRatio(
        item.str,
        0,
        wordStartInLine
      );
      const wordEndRatio = calculateCharacterWidthRatio(
        item.str,
        0,
        wordEndInLine
      );

      const wordX0 = lineX0 + lineWidth * wordStartRatio;
      const wordX1 = lineX0 + lineWidth * wordEndRatio;

      wordDataArray.push({
        uid: baseUid + wordIndex++,
        text: word,
        bbox: {
          x0: wordX0,
          y0: lineY0,
          x1: wordX1,
          y1: lineY1,
        },
        confidence: 100,
        line: lineInfo,
      });
    });
  });

  return wordDataArray;
}
</script>

<style scoped>
.fullscreen {
  position: relative;
  width: 100%;
  height: calc(100vh - var(--el-main-padding) * 2);

  --van-swipe-indicator-size: 12px;
  --van-swipe-indicator-inactive-background: grey;
}

.custom-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 100;
}

@media screen and (orientation: landscape) {
  .aside-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px 8px;
  }

  .aside-container .aside-button {
    width: 100%;
  }

  .aside-container .aside-button:not(:last-child) {
    margin-bottom: 15px;
  }

  .aside-container-portrait {
    display: none;
  }
}

@media screen and (orientation: portrait) {
  .aside-container {
    display: none;
  }

  .aside-container-portrait {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 80px;
    padding: 20px 8px;
  }

  .aside-container-portrait .aside-button {
    max-width: 128px;
    width: 20%;
    min-width: 32px;
  }
}

@media screen and (orientation: portrait) {
  .fullscreen {
    height: 100%;
  }

  .el-aside {
    width: 100%;
  }

  .el-container {
    flex-direction: column;
    height: 100vh;
  }
}
</style>
