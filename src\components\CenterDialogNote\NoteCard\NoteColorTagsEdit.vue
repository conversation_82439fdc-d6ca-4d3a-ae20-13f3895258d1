<template>
  <el-dialog
    v-model="showEditDialog"
    :show-close="false"
    title="颜色自定义"
    width="50%"
    :append-to-body="true"
    :destroy-on-close="true"
    :style="{ minWidth: '350px' }"
  >
    <ColorTagsEditCard
      v-for="item in colorTags"
      :key="item.id"
      :tag="item"
      @edit-finish="onEditFinish"
      @delete-tag="onDelete"
    />
    <ColorTagsNew @create-finish="addTag" />
    <div style="display: flex; justify-content: flex-end; padding-top: 20px">
      <el-button type="success" @click="closeDialog">完成</el-button>
      <el-button type="danger" @click="showEditDialog = false">取消</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import dayjs from "dayjs";
import { listColorTags, saveColorTags } from "@/lib/RectDatabase";
import { ColorTags as DefaultColorTags } from "@/components/CenterDialogNote/NoteCard/DefaultValues";
import ColorTagsEditCard from "@/components/CenterDialogNote/NoteCard/ColorTagsEditCard.vue"; // 编辑标签
import ColorTagsNew from "@/components/CenterDialogNote/NoteCard/NoteColorTagsNew.vue"; // 新增标签

const emits = defineEmits(["edit-finish"]);

const colorTags = ref([]);
const showEditDialog = ref(false);

watch(
  () => showEditDialog.value,
  (val) => {
    if (val) {
      loadColorTags();
    }
  }
);

async function loadColorTags() {
  const list = await listColorTags();
  if (list.length === 0) {
    colorTags.value = DefaultColorTags;
  } else {
    colorTags.value = list;
  }
}

// show dialog
const toggleDialog = () => {
  showEditDialog.value = true;
};

// close dialog and save data
const closeDialog = () => {
  showEditDialog.value = false;
  saveColorTags(colorTags.value);
  emits("edit-finish", colorTags.value);
};

const onEditFinish = (tag) => {
  colorTags.value = colorTags.value.map((item) => {
    if (item.id === tag.id) {
      return tag;
    }
    return item;
  });
};

const onDelete = (tag) => {
  colorTags.value = colorTags.value.filter((item) => item.id !== tag.id);
};

const addTag = (tag) => {
  const list = colorTags.value;
  list.push({ ...tag, color: "rgb(21, 126, 251)" });

  const uid = dayjs().unix();
  colorTags.value = list.map((item, index) => {
    item.id = uid + index;
    return item;
  });
};

defineExpose({
  toggleDialog,
});
</script>
