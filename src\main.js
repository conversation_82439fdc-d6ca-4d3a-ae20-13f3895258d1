import "./assets/main.css";

import { createApp } from "vue";
import { createRouter, createWebHashHistory } from "vue-router";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import Vant from "vant";
import Vue<PERSON>onva from "vue-konva";

// 本地化
import zhCn from "element-plus/es/locale/lang/zh-cn";
import "dayjs/locale/zh-cn";

import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import "element-plus/dist/index.css";
import "bootstrap-icons/font/bootstrap-icons.css";
import "vant/lib/index.css";

import { GlobalWorkerOptions } from "pdfjs-dist";
GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url
).toString();

import App from "./App.vue";
import AppRouter from "./AppRouter.vue";
import MainEntry from "./MainEntry.vue";
import PayReturn from "./PayReturn.vue";
import ImagePreview from "@/components/ImagePreview/ImagePreview.vue";
import PhotoPreview from "@/components/PhotoTaker/PhotoPreview.vue";
import PDFPreview from "./components/PDFPreview/PDFPreview.vue";
import FileFolder from "@/components/FileFolder/FileFolder.vue";
import SearchPage from "@/components/FileFolder/SearchPage/SearchPage.vue";
import StatisticsPage from "@/components/FileFolder/SearchPage/StatisticsPage.vue";
import ReviewSearchPage from "@/components/Review/ReviewSearch/ReviewSearchPage.vue";
import ProfileLoginPage from "@/components/Profile/LoginPage/LoginIndex.vue";
import ProfileIndex from "@/components/Profile/ProfileIndex.vue";
import ProfileLoginMethod from "@/components/Profile/LoginMethod.vue";
import ProfilePrivacyPolicy from "@/components/Profile/PrivacyPolicy.vue";
import ProfileSetting from "@/components/Profile/ProfileSetting.vue";
import ProfileMemberInfo from "@/components/Profile/MemberInfo.vue";
import AdminPanel from "@/components/AdminPanel/AdminIndex.vue";

const routes = [
  { path: "/", component: MainEntry },
  { path: "/app", component: App },
  { path: "/cropper-preview", component: ImagePreview },
  { path: "/photo-preview", component: PhotoPreview },
  { path: "/pdf-preview", component: PDFPreview },
  { path: "/file", name: "root", component: FileFolder },
  { path: "/search-page", component: SearchPage },
  { path: "/statistics-page", component: StatisticsPage },
  { path: "/review-search", component: ReviewSearchPage },
  { path: "/login", component: ProfileLoginPage },
  { path: "/profile", component: ProfileIndex },
  { path: "/profile/login-method", component: ProfileLoginMethod },
  { path: "/profile/privacy-policy", component: ProfilePrivacyPolicy },
  { path: "/profile/setting", component: ProfileSetting },
  { path: "/profile/member-info", component: ProfileMemberInfo },
  { path: "/pay-return", name: "pay-return", component: PayReturn },
  { path: "/admin", component: AdminPanel },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

const app = createApp(AppRouter);

app.use(router);
app.use(createPinia());
app.use(ElementPlus, {
  locale: zhCn,
});
app.use(Vant);
app.use(VueKonva);

// element plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.mount("#app");
