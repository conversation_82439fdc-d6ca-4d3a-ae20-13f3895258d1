<template>
  <van-floating-bubble
    v-model:offset="offset"
    icon="plus"
    @touchend.prevent="onClick"
    @offset-change="onOffsetChange"
  />
  <van-action-sheet
    v-model:show="show"
    :actions="sheetActions"
    cancel-text="取消"
    close-on-click-action
    @cancel="onCancel"
    @select="onSelect"
  >
    <div v-if="showFreeTrialWarning">
      <h4 class="trial-warning">
        <div></div>
        <div v-if="profileStore.freeTrialCount > 0">
          剩余试用次数：
          <el-text type="danger">
            {{ profileStore.freeTrialCount }}
          </el-text>
        </div>
        <div v-else>
          <el-text type="danger">试用已结束</el-text>
        </div>
        <div>
          <el-button
            size="small"
            link
            type="danger"
            @click="gotoMemberInfoPage"
          >
            开通会员
          </el-button>
        </div>
      </h4>
    </div>
  </van-action-sheet>
  <input
    ref="pdfInputRef"
    type="file"
    accept="application/pdf"
    @change="uploadFileChange"
    style="display: none"
  />
  <input
    ref="cameraInputRef"
    type="file"
    accept="image/*"
    capture="environment"
    @change="takePhotoChange"
    style="display: none"
  />
  <input
    ref="photoInputRef"
    type="file"
    accept="image/*"
    multiple
    @change="uploadPictureChange"
    style="display: none"
  />
</template>

<script setup>
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  useDebounceFn,
  useEventListener,
  useResizeObserver,
} from "@vueuse/core";
import { useAppStore } from "@/stores/app.js";
import { useProfileStore } from "@/stores/profile";
import {
  createNewFileMeta,
  putFileMeta,
  updateParentFolderFileCount,
} from "@/lib/FileList.js";
import { showAskFilenameDialog } from "@/components/FileFolder/Helper/AskFilenameDialog.jsx";

const router = useRouter();
const appStore = useAppStore();
const profileStore = useProfileStore();

const pdfInputRef = ref();
const cameraInputRef = ref();
const photoInputRef = ref();

const offset = ref({ x: window.innerWidth - 70, y: window.innerHeight - 150 });
const show = ref(false);
let isOffsetChange = false;
const actions = [
  // { name: "新建笔记" },
  { name: "新建文件夹" },
  { name: "导入PDF" },
  { name: "拍照" },
  { name: "相册" },
];

const actionsLock = [
  // { name: "新建笔记", icon: "lock" },
  { name: "新建文件夹" },
  { name: "导入PDF", icon: "lock" },
  { name: "拍照", icon: "lock" },
  { name: "相册", icon: "lock" },
];

const sheetActions = computed(() => {
  if (!profileStore.haveTrialPermission) {
    return actionsLock;
  } else {
    return actions;
  }
});

const showFreeTrialWarning = computed(() => {
  return (
    !profileStore.isVIP &&
    profileStore.freeTrialCount >= 0 &&
    profileStore.freeTrialCount <= 3
  );
});

const resizeCallback = useDebounceFn(() => {
  offset.value = { x: window.innerWidth - 70, y: window.innerHeight - 150 };
}, 100);
useResizeObserver(document.body, resizeCallback);

useEventListener("message", (event) => {
  if (event?.data?.action === "expo-take-photo") {
    takePhoto();
  }
});

const onCancel = () => {
  show.value = false;
};

function onOffsetChange() {
  isOffsetChange = true;
}

const onClick = useDebounceFn(() => {
  if (isOffsetChange) {
    isOffsetChange = false;
    return;
  }
  show.value = true;
}, 50);

function checkPermission() {
  if (!profileStore.haveTrialPermission) {
    ElMessage.warning("试用已结束，请升级会员");
    return false;
  }
  return true;
}

function gotoMemberInfoPage() {
  router.push("/profile/member-info");
}

function onSelect(item) {
  if (!checkPermission()) {
    return;
  }

  if (item.name === "新建笔记") {
    createNote();
  } else if (item.name === "新建文件夹") {
    createFolder();
  } else if (item.name === "导入PDF") {
    uploadFile();
  } else if (item.name === "拍照") {
    checkCameraPermission();
  } else if (item.name === "相册") {
    uploadPicture();
  }
}

async function createNote() {
  const filename = await showAskFilenameDialog("文件", "");
  const timestamp = Date.now();
  const fileMeta = createNewFileMeta(
    timestamp,
    appStore.currentFolderId,
    "file",
    filename,
    0
  );
  await putFileMeta(fileMeta);
  await updateParentFolderFileCount(fileMeta, 1);
}

async function createFolder() {
  const folderName = await showAskFilenameDialog("文件夹", "");
  const timestamp = Date.now();
  const fileMeta = createNewFileMeta(
    timestamp,
    appStore.currentFolderId,
    "folder",
    folderName,
    0
  );
  await putFileMeta(fileMeta);
  await updateParentFolderFileCount(fileMeta, 1);
}

function uploadFile() {
  pdfInputRef.value?.click();
}

function uploadFileChange(event) {
  // 获取文件列表
  const selectedFiles = event.target?.files;
  appStore.currentFileMeta = null;
  if (selectedFiles.length > 0) {
    const file = selectedFiles[0];
    appStore.currentFile = file;
    router.replace("/pdf-preview");
  }
}

function checkCameraPermission() {
  if (typeof ReactNativeWebView !== "undefined") {
    const data = {
      action: "check-camera-permission",
    };
    // eslint-disable-next-line no-undef
    ReactNativeWebView.postMessage(JSON.stringify(data));
  } else {
    ElMessage.error("无法获取拍照权限");
  }
}

function takePhoto() {
  cameraInputRef.value?.click();
}

function takePhotoChange(event) {
  // 获取文件列表
  const selectedFiles = event.target?.files;
  appStore.currentFileMeta = null;
  if (selectedFiles.length > 0) {
    const files = [];
    for (let i = 0; i < selectedFiles.length; i++) {
      files.push(selectedFiles[i]);
    }
    appStore.currentFile = files;
    router.push("/photo-preview");
  }
}

function uploadPicture() {
  photoInputRef.value?.click();
}

function uploadPictureChange(event) {
  // 获取文件列表
  const selectedFiles = event.target?.files;
  appStore.currentFileMeta = null;
  if (selectedFiles.length > 0) {
    const files = [];
    for (let i = 0; i < selectedFiles.length; i++) {
      files.push(selectedFiles[i]);
    }
    appStore.currentFile = files;
    router.push("/cropper-preview");
  }
}
</script>

<style lang="scss" scoped>
.trial-warning {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  width: 100%;

  div:nth-child(3) {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: 5px;
  }
}
</style>
