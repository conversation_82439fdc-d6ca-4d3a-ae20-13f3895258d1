<template>
  <FloatCircle
    :custom-tools="customTools"
    :default-direction="toolboxDirection"
    :default-position="toolboxPosition"
    :reset-position-key="toolboxResetKey"
    @change-tool-current="changeToolCurrent"
  >
    <template #extra-btn>
      <div class="tools-button-outer">
        <el-button
          type="success"
          small
          :icon="Check"
          style="width: 80%"
          @click="toggleDrawing"
        ></el-button>
      </div>
    </template>
  </FloatCircle>
</template>

<script setup>
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useNotePenStore } from "@/stores/note-pen";
import { Check } from "@element-plus/icons-vue";
import FloatCircle from "@/components/ToolSelection/FloatCircle.vue";

const props = defineProps({
  fullscreen: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["toggle-drawing"]);

const notePenStore = useNotePenStore();
const { isNotePenToolShow, aliveTool, aliveColor, aliveLineWidth } =
  storeToRefs(notePenStore);

const toolboxDirection = ref("column");
const toolboxPosition = ref({
  top: "200px",
  left: "40px",
});
const toolboxResetKey = ref(Date.now());

watch(
  () => [props.fullscreen],
  () => {
    if (props.fullscreen) {
      toolboxDirection.value = "row";
      toolboxPosition.value = { top: "55px", left: "55%" };
    } else {
      toolboxDirection.value = "column";
      toolboxPosition.value = { top: "200px", left: "40px" };
    }

    nextTick(() => toolboxResetKey.value++);
  },
  {
    immediate: true,
  }
);

const customTools = [
  {
    toolIndex: 1,
    type: "PEN",
    aliveColor: "rgb(210, 19, 19)",
    lineWidth: 5,
  },
  {
    toolIndex: 2,
    type: "ERASER",
    aliveColor: "rgb(213, 114, 114)", //用于图标背景和边框 不参与实际书写区域的功能
    lineWidth: 14,
  },
];

onMounted(() => {
  isNotePenToolShow.value = true;
  aliveTool.value = customTools[0].type;
  aliveColor.value = customTools[0].aliveColor;
  aliveLineWidth.value = customTools[0].lineWidth;
});

onBeforeUnmount(() => {
  isNotePenToolShow.value = false;
});

function changeToolCurrent(tool) {
  aliveTool.value = tool.type;
  aliveColor.value = tool.aliveColor;
  aliveLineWidth.value = tool.lineWidth;
}

function toggleDrawing() {
  emits("toggle-drawing");
}
</script>

<style scoped lang="scss">
.tools-button-outer {
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  display: flex;
}
</style>
